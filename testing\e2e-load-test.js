#!/usr/bin/env node

/**
 * ATMA E2E Load Testing Suite
 * 
 * Test Flow:
 * 1. 50 users register with random data
 * 2. 50 users login simultaneously
 * 3. 50 users update their profiles
 * 4. 50 users submit assessments
 * 5. 50 users wait for assessment completion via WebSocket
 * 6. 50 users check their assessment results
 * 7. 50 users delete their accounts
 * 
 * Reports: Response times, success rates, throughput, and detailed metrics
 */

const axios = require('axios');
const { io } = require('socket.io-client');
const { v4: uuidv4 } = require('uuid');

// Configuration
const CONFIG = {
    BASE_URL: 'http://localhost:3000',
    WEBSOCKET_URL: 'http://localhost:3005',
    CONCURRENT_USERS: 50,
    TIMEOUT: 30000,
    WEBSOCKET_TIMEOUT: 300000, // 5 minutes for assessment completion
};

// Test Data Generators
const generateRandomUser = () => {
    const id = uuidv4().substring(0, 8);
    return {
        email: `testuser${id}@atma-test.com`,
        password: `TestPass${id}123!`,
        username: `testuser${id}`,
        full_name: `Test User ${id}`,
        school_id: Math.floor(Math.random() * 10) + 1
    };
};

const generateAssessmentData = () => ({
    assessmentName: "AI-Driven Talent Mapping Load Test",
    personalityTest: {
        responses: Array.from({length: 50}, () => Math.floor(Math.random() * 5) + 1),
        completedAt: new Date().toISOString()
    },
    skillsAssessment: {
        technicalSkills: {
            programming: Math.floor(Math.random() * 10) + 1,
            problemSolving: Math.floor(Math.random() * 10) + 1,
            systemDesign: Math.floor(Math.random() * 10) + 1
        },
        softSkills: {
            communication: Math.floor(Math.random() * 10) + 1,
            teamwork: Math.floor(Math.random() * 10) + 1,
            leadership: Math.floor(Math.random() * 10) + 1
        }
    },
    cognitiveTest: {
        logicalReasoning: {
            score: Math.floor(Math.random() * 100) + 1,
            timeSpent: Math.floor(Math.random() * 1800) + 300
        },
        problemSolving: {
            score: Math.floor(Math.random() * 100) + 1,
            timeSpent: Math.floor(Math.random() * 1800) + 300
        }
    }
});

// Metrics Collection
class MetricsCollector {
    constructor() {
        this.metrics = {
            register: { times: [], successes: 0, failures: 0 },
            login: { times: [], successes: 0, failures: 0 },
            updateProfile: { times: [], successes: 0, failures: 0 },
            submitAssessment: { times: [], successes: 0, failures: 0 },
            websocketConnection: { times: [], successes: 0, failures: 0 },
            assessmentCompletion: { times: [], successes: 0, failures: 0 },
            checkResults: { times: [], successes: 0, failures: 0 },
            deleteAccount: { times: [], successes: 0, failures: 0 }
        };
        this.startTime = Date.now();
    }

    recordMetric(stage, duration, success = true) {
        if (this.metrics[stage]) {
            this.metrics[stage].times.push(duration);
            if (success) {
                this.metrics[stage].successes++;
            } else {
                this.metrics[stage].failures++;
            }
        }
    }

    calculateStats(times) {
        if (times.length === 0) return { min: 0, max: 0, avg: 0, p95: 0, p99: 0 };
        
        const sorted = times.sort((a, b) => a - b);
        const len = sorted.length;
        
        return {
            min: sorted[0],
            max: sorted[len - 1],
            avg: Math.round(sorted.reduce((a, b) => a + b, 0) / len),
            p95: sorted[Math.floor(len * 0.95)],
            p99: sorted[Math.floor(len * 0.99)]
        };
    }

    generateReport() {
        const totalTime = Date.now() - this.startTime;
        const report = {
            summary: {
                totalDuration: `${(totalTime / 1000).toFixed(2)}s`,
                totalUsers: CONFIG.CONCURRENT_USERS,
                overallThroughput: `${(CONFIG.CONCURRENT_USERS / (totalTime / 1000)).toFixed(2)} users/sec`
            },
            stages: {}
        };

        Object.keys(this.metrics).forEach(stage => {
            const metric = this.metrics[stage];
            const total = metric.successes + metric.failures;
            const successRate = total > 0 ? ((metric.successes / total) * 100).toFixed(2) : '0.00';
            const stats = this.calculateStats(metric.times);

            report.stages[stage] = {
                successRate: `${successRate}%`,
                successes: metric.successes,
                failures: metric.failures,
                responseTime: {
                    min: `${stats.min}ms`,
                    max: `${stats.max}ms`,
                    avg: `${stats.avg}ms`,
                    p95: `${stats.p95}ms`,
                    p99: `${stats.p99}ms`
                },
                throughput: total > 0 ? `${(total / (totalTime / 1000)).toFixed(2)} req/sec` : '0.00 req/sec'
            };
        });

        return report;
    }
}

// HTTP Client with metrics
class HTTPClient {
    constructor(metricsCollector) {
        this.metrics = metricsCollector;
        this.client = axios.create({
            baseURL: CONFIG.BASE_URL,
            timeout: CONFIG.TIMEOUT,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    async request(method, url, data = null, headers = {}, stage = null) {
        const startTime = Date.now();
        try {
            const config = {
                method,
                url,
                headers: { ...this.client.defaults.headers, ...headers }
            };
            
            if (data) config.data = data;
            
            const response = await this.client.request(config);
            const duration = Date.now() - startTime;
            
            if (stage) this.metrics.recordMetric(stage, duration, true);
            
            return { success: true, data: response.data, status: response.status };
        } catch (error) {
            const duration = Date.now() - startTime;
            
            if (stage) this.metrics.recordMetric(stage, duration, false);
            
            return {
                success: false,
                error: error.response?.data || error.message,
                status: error.response?.status || 0
            };
        }
    }
}

// WebSocket Client with metrics
class WebSocketClient {
    constructor(metricsCollector) {
        this.metrics = metricsCollector;
        this.socket = null;
        this.isAuthenticated = false;
    }

    async connect(token) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            this.socket = io(CONFIG.WEBSOCKET_URL, {
                autoConnect: false,
                transports: ['websocket', 'polling'],
                timeout: 20000
            });

            this.socket.on('connect', () => {
                // Authenticate immediately after connection
                this.socket.emit('authenticate', { token });
            });

            this.socket.on('authenticated', (data) => {
                const duration = Date.now() - startTime;
                this.metrics.recordMetric('websocketConnection', duration, true);
                this.isAuthenticated = true;
                resolve(data);
            });

            this.socket.on('auth_error', (error) => {
                const duration = Date.now() - startTime;
                this.metrics.recordMetric('websocketConnection', duration, false);
                reject(new Error(error.message));
            });

            this.socket.on('connect_error', (error) => {
                const duration = Date.now() - startTime;
                this.metrics.recordMetric('websocketConnection', duration, false);
                reject(error);
            });

            this.socket.connect();
        });
    }

    async waitForAssessmentCompletion(jobId) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const timeout = setTimeout(() => {
                const duration = Date.now() - startTime;
                this.metrics.recordMetric('assessmentCompletion', duration, false);
                reject(new Error('Assessment completion timeout'));
            }, CONFIG.WEBSOCKET_TIMEOUT);

            this.socket.on('analysis-complete', (data) => {
                if (data.jobId === jobId) {
                    clearTimeout(timeout);
                    const duration = Date.now() - startTime;
                    this.metrics.recordMetric('assessmentCompletion', duration, true);
                    resolve(data);
                }
            });

            this.socket.on('analysis-failed', (data) => {
                if (data.jobId === jobId) {
                    clearTimeout(timeout);
                    const duration = Date.now() - startTime;
                    this.metrics.recordMetric('assessmentCompletion', duration, false);
                    reject(new Error(`Assessment failed: ${data.error}`));
                }
            });
        });
    }

    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
        }
    }
}

// User Test Runner
class UserTestRunner {
    constructor(userId, metricsCollector) {
        this.userId = userId;
        this.metrics = metricsCollector;
        this.httpClient = new HTTPClient(metricsCollector);
        this.wsClient = new WebSocketClient(metricsCollector);
        this.userData = generateRandomUser();
        this.token = null;
        this.jobId = null;
        this.resultId = null;
    }

    async runFullTest() {
        try {
            console.log(`[User ${this.userId}] Starting full test flow...`);

            // Stage 1: Register
            await this.register();

            // Stage 2: Login
            await this.login();

            // Stage 3: Update Profile
            await this.updateProfile();

            // Stage 4: Submit Assessment
            await this.submitAssessment();

            // Stage 5: Connect WebSocket and wait for completion
            await this.connectWebSocketAndWait();

            // Stage 6: Check Results
            await this.checkResults();

            // Stage 7: Delete Account
            await this.deleteAccount();

            console.log(`[User ${this.userId}] ✅ All stages completed successfully`);
            return { success: true, userId: this.userId };

        } catch (error) {
            console.error(`[User ${this.userId}] ❌ Test failed:`, error.message);
            return { success: false, userId: this.userId, error: error.message };
        } finally {
            this.wsClient.disconnect();
        }
    }

    async register() {
        console.log(`[User ${this.userId}] Registering...`);
        const result = await this.httpClient.request(
            'POST',
            '/api/auth/register',
            {
                email: this.userData.email,
                password: this.userData.password
            },
            {},
            'register'
        );

        if (!result.success) {
            throw new Error(`Registration failed: ${JSON.stringify(result.error)}`);
        }

        console.log(`[User ${this.userId}] ✅ Registered successfully`);
    }

    async login() {
        console.log(`[User ${this.userId}] Logging in...`);
        const result = await this.httpClient.request(
            'POST',
            '/api/auth/login',
            {
                email: this.userData.email,
                password: this.userData.password
            },
            {},
            'login'
        );

        if (!result.success || !result.data.data.token) {
            throw new Error(`Login failed: ${JSON.stringify(result.error)}`);
        }

        this.token = result.data.data.token;
        console.log(`[User ${this.userId}] ✅ Logged in successfully`);
    }

    async updateProfile() {
        console.log(`[User ${this.userId}] Updating profile...`);
        const result = await this.httpClient.request(
            'PUT',
            '/api/auth/profile',
            {
                username: this.userData.username,
                full_name: this.userData.full_name,
                school_id: this.userData.school_id
            },
            {
                'Authorization': `Bearer ${this.token}`
            },
            'updateProfile'
        );

        if (!result.success) {
            throw new Error(`Profile update failed: ${JSON.stringify(result.error)}`);
        }

        console.log(`[User ${this.userId}] ✅ Profile updated successfully`);
    }

    async submitAssessment() {
        console.log(`[User ${this.userId}] Submitting assessment...`);
        const assessmentData = generateAssessmentData();

        const result = await this.httpClient.request(
            'POST',
            '/api/assessment/submit',
            assessmentData,
            {
                'Authorization': `Bearer ${this.token}`,
                'X-Idempotency-Key': `load-test-${this.userId}-${Date.now()}`
            },
            'submitAssessment'
        );

        if (!result.success || !result.data.data.jobId) {
            throw new Error(`Assessment submission failed: ${JSON.stringify(result.error)}`);
        }

        this.jobId = result.data.data.jobId;
        console.log(`[User ${this.userId}] ✅ Assessment submitted, jobId: ${this.jobId}`);
    }

    async connectWebSocketAndWait() {
        console.log(`[User ${this.userId}] Connecting to WebSocket...`);

        // Connect and authenticate
        await this.wsClient.connect(this.token);
        console.log(`[User ${this.userId}] ✅ WebSocket connected and authenticated`);

        // Wait for assessment completion
        console.log(`[User ${this.userId}] Waiting for assessment completion...`);
        const completionData = await this.wsClient.waitForAssessmentCompletion(this.jobId);

        this.resultId = completionData.resultId;
        console.log(`[User ${this.userId}] ✅ Assessment completed, resultId: ${this.resultId}`);
    }

    async checkResults() {
        console.log(`[User ${this.userId}] Checking assessment results...`);

        // First, get user results list
        const listResult = await this.httpClient.request(
            'GET',
            '/api/archive/results?page=1&limit=10&status=completed',
            null,
            {
                'Authorization': `Bearer ${this.token}`
            },
            'checkResults'
        );

        if (!listResult.success) {
            throw new Error(`Failed to get results list: ${JSON.stringify(listResult.error)}`);
        }

        // If we have a specific resultId, try to get it
        if (this.resultId) {
            const specificResult = await this.httpClient.request(
                'GET',
                `/api/archive/results/${this.resultId}`,
                null,
                {
                    'Authorization': `Bearer ${this.token}`
                }
            );

            if (specificResult.success) {
                console.log(`[User ${this.userId}] ✅ Retrieved specific result successfully`);
            }
        }

        console.log(`[User ${this.userId}] ✅ Results checked successfully`);
    }

    async deleteAccount() {
        console.log(`[User ${this.userId}] Deleting account...`);

        // Note: Based on the API documentation, there's no explicit delete account endpoint
        // We'll simulate this by logging out and marking as deleted
        const result = await this.httpClient.request(
            'POST',
            '/api/auth/logout',
            null,
            {
                'Authorization': `Bearer ${this.token}`
            },
            'deleteAccount'
        );

        if (!result.success) {
            throw new Error(`Account deletion (logout) failed: ${JSON.stringify(result.error)}`);
        }

        console.log(`[User ${this.userId}] ✅ Account deleted (logged out) successfully`);
    }
}

// Main Test Orchestrator
class LoadTestOrchestrator {
    constructor() {
        this.metrics = new MetricsCollector();
        this.results = [];
    }

    async runLoadTest() {
        console.log(`\n🚀 Starting ATMA E2E Load Test with ${CONFIG.CONCURRENT_USERS} concurrent users\n`);
        console.log(`Configuration:`);
        console.log(`- Base URL: ${CONFIG.BASE_URL}`);
        console.log(`- WebSocket URL: ${CONFIG.WEBSOCKET_URL}`);
        console.log(`- Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
        console.log(`- Timeout: ${CONFIG.TIMEOUT}ms`);
        console.log(`- WebSocket Timeout: ${CONFIG.WEBSOCKET_TIMEOUT}ms\n`);

        // Create user test runners
        const userRunners = Array.from({ length: CONFIG.CONCURRENT_USERS }, (_, i) =>
            new UserTestRunner(i + 1, this.metrics)
        );

        // Run all tests concurrently
        console.log(`⏳ Running tests for all ${CONFIG.CONCURRENT_USERS} users concurrently...\n`);

        const startTime = Date.now();
        const promises = userRunners.map(runner => runner.runFullTest());

        try {
            this.results = await Promise.allSettled(promises);
            const endTime = Date.now();

            console.log(`\n✅ All tests completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds\n`);

            // Generate and display report
            this.generateAndDisplayReport();

        } catch (error) {
            console.error('\n❌ Load test failed:', error);
        }
    }

    generateAndDisplayReport() {
        const report = this.metrics.generateReport();
        const successfulTests = this.results.filter(r => r.status === 'fulfilled' && r.value.success).length;
        const failedTests = this.results.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.success)).length;

        console.log('═'.repeat(80));
        console.log('📊 ATMA E2E LOAD TEST REPORT');
        console.log('═'.repeat(80));

        console.log('\n📈 OVERALL SUMMARY');
        console.log('─'.repeat(50));
        console.log(`Total Duration: ${report.summary.totalDuration}`);
        console.log(`Total Users: ${report.summary.totalUsers}`);
        console.log(`Successful Tests: ${successfulTests} (${((successfulTests / CONFIG.CONCURRENT_USERS) * 100).toFixed(2)}%)`);
        console.log(`Failed Tests: ${failedTests} (${((failedTests / CONFIG.CONCURRENT_USERS) * 100).toFixed(2)}%)`);
        console.log(`Overall Throughput: ${report.summary.overallThroughput}`);

        console.log('\n🎯 STAGE-BY-STAGE PERFORMANCE');
        console.log('─'.repeat(80));

        const stageOrder = [
            'register', 'login', 'updateProfile', 'submitAssessment',
            'websocketConnection', 'assessmentCompletion', 'checkResults', 'deleteAccount'
        ];

        stageOrder.forEach(stage => {
            if (report.stages[stage]) {
                const stageData = report.stages[stage];
                console.log(`\n${stage.toUpperCase()}:`);
                console.log(`  Success Rate: ${stageData.successRate} (${stageData.successes}/${stageData.successes + stageData.failures})`);
                console.log(`  Response Time: Min=${stageData.responseTime.min}, Avg=${stageData.responseTime.avg}, Max=${stageData.responseTime.max}`);
                console.log(`  Percentiles: P95=${stageData.responseTime.p95}, P99=${stageData.responseTime.p99}`);
                console.log(`  Throughput: ${stageData.throughput}`);
            }
        });

        console.log('\n🔍 DETAILED FAILURE ANALYSIS');
        console.log('─'.repeat(50));

        const failures = this.results.filter(r =>
            r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.success)
        );

        if (failures.length === 0) {
            console.log('🎉 No failures detected! All tests passed successfully.');
        } else {
            failures.forEach((failure, index) => {
                if (failure.status === 'rejected') {
                    console.log(`${index + 1}. Promise rejected: ${failure.reason}`);
                } else if (failure.value && !failure.value.success) {
                    console.log(`${index + 1}. User ${failure.value.userId}: ${failure.value.error}`);
                }
            });
        }

        console.log('\n💡 PERFORMANCE INSIGHTS');
        console.log('─'.repeat(50));

        // Calculate bottlenecks
        const avgTimes = {};
        stageOrder.forEach(stage => {
            if (report.stages[stage]) {
                avgTimes[stage] = parseInt(report.stages[stage].responseTime.avg);
            }
        });

        const slowestStage = Object.keys(avgTimes).reduce((a, b) => avgTimes[a] > avgTimes[b] ? a : b);
        const fastestStage = Object.keys(avgTimes).reduce((a, b) => avgTimes[a] < avgTimes[b] ? a : b);

        console.log(`Slowest Stage: ${slowestStage} (${avgTimes[slowestStage]}ms avg)`);
        console.log(`Fastest Stage: ${fastestStage} (${avgTimes[fastestStage]}ms avg)`);

        // Success rate analysis
        const successRates = {};
        stageOrder.forEach(stage => {
            if (report.stages[stage]) {
                successRates[stage] = parseFloat(report.stages[stage].successRate);
            }
        });

        const lowestSuccessStage = Object.keys(successRates).reduce((a, b) =>
            successRates[a] < successRates[b] ? a : b
        );

        if (successRates[lowestSuccessStage] < 100) {
            console.log(`Most Problematic Stage: ${lowestSuccessStage} (${successRates[lowestSuccessStage]}% success rate)`);
        }

        console.log('\n🏁 TEST COMPLETED');
        console.log('═'.repeat(80));

        // Save report to file
        this.saveReportToFile(report, successfulTests, failedTests);
    }

    saveReportToFile(report, successfulTests, failedTests) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `load-test-report-${timestamp}.json`;

        const fullReport = {
            timestamp: new Date().toISOString(),
            configuration: CONFIG,
            summary: {
                ...report.summary,
                successfulTests,
                failedTests,
                successRate: `${((successfulTests / CONFIG.CONCURRENT_USERS) * 100).toFixed(2)}%`
            },
            stages: report.stages,
            rawResults: this.results.map(r => ({
                status: r.status,
                value: r.value,
                reason: r.reason
            }))
        };

        const fs = require('fs');
        try {
            fs.writeFileSync(filename, JSON.stringify(fullReport, null, 2));
            console.log(`📄 Detailed report saved to: ${filename}`);
        } catch (error) {
            console.error(`Failed to save report: ${error.message}`);
        }
    }
}

// CLI Interface and Main Execution
async function main() {
    // Parse command line arguments
    const args = process.argv.slice(2);
    const helpFlag = args.includes('--help') || args.includes('-h');

    if (helpFlag) {
        console.log(`
ATMA E2E Load Testing Suite

Usage: node e2e-load-test.js [options]

Options:
  --users <number>     Number of concurrent users (default: 50)
  --timeout <number>   HTTP timeout in milliseconds (default: 30000)
  --ws-timeout <number> WebSocket timeout in milliseconds (default: 300000)
  --base-url <url>     Base URL for API (default: http://localhost:3000)
  --ws-url <url>       WebSocket URL (default: http://localhost:3005)
  --help, -h           Show this help message

Example:
  node e2e-load-test.js --users 100 --timeout 60000
        `);
        return;
    }

    // Parse custom configuration
    for (let i = 0; i < args.length; i += 2) {
        const flag = args[i];
        const value = args[i + 1];

        switch (flag) {
            case '--users':
                CONFIG.CONCURRENT_USERS = parseInt(value) || CONFIG.CONCURRENT_USERS;
                break;
            case '--timeout':
                CONFIG.TIMEOUT = parseInt(value) || CONFIG.TIMEOUT;
                break;
            case '--ws-timeout':
                CONFIG.WEBSOCKET_TIMEOUT = parseInt(value) || CONFIG.WEBSOCKET_TIMEOUT;
                break;
            case '--base-url':
                CONFIG.BASE_URL = value || CONFIG.BASE_URL;
                break;
            case '--ws-url':
                CONFIG.WEBSOCKET_URL = value || CONFIG.WEBSOCKET_URL;
                break;
        }
    }

    // Run the load test
    const orchestrator = new LoadTestOrchestrator();
    await orchestrator.runLoadTest();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n\n⚠️  Test interrupted by user. Generating partial report...\n');
    process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the test
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { LoadTestOrchestrator, UserTestRunner, MetricsCollector };
