# Assessment Service - External API Documentation

## Overview
Assessment Service menyediakan API untuk submit dan monitor assessment AI-driven talent mapping. API ini diakses melalui **API Gateway** pada port **3000** dengan prefix `/api/assessment/`.

**Service Information:**
- **Service Name:** assessment-service
- **Internal Port:** 3003
- **External Access:** Via API Gateway (Port 3000)
- **Base URL:** `http://localhost:3000/api/assessment/`
- **Version:** 1.0.0

## Authentication
Semua endpoint eksternal memerlukan autentikasi JWT token yang diperoleh dari Auth Service.

**Header Required:**
```
Authorization: Bearer <jwt_token>
```

## Rate Limiting
- **Assessment Endpoints:** 100 requests per 15 minutes per user
- **General Gateway:** 5000 requests per 15 minutes

---

## 🎯 Assessment Submission Endpoints

### 1. Submit Assessment
**POST** `/api/assessment/submit`

Submit data assessment untuk dianalisis oleh AI.

**Required Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Idempotency-Key: <unique_key> (optional)
```

**Request Body:**
```json
{
  "assessmentName": "AI-Driven Talent Mapping",
  "personalityTest": {
    "responses": [1, 2, 3, 4, 5],
    "completedAt": "2024-01-01T10:00:00Z"
  },
  "skillsAssessment": {
    "technicalSkills": {...},
    "softSkills": {...},
    "completedAt": "2024-01-01T10:15:00Z"
  },
  "cognitiveTest": {
    "logicalReasoning": {...},
    "problemSolving": {...},
    "completedAt": "2024-01-01T10:30:00Z"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "uuid",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 3,
    "tokenCost": 1,
    "remainingTokens": 9
  }
}
```

**Features:**
- **Token Deduction:** Otomatis potong token dari saldo user
- **Idempotency:** Prevent duplicate submission dengan idempotency key
- **Queue Position:** Informasi posisi dalam antrian processing
- **Real-time Tracking:** Job ID untuk tracking status

**Error Responses:**
- `401 UNAUTHORIZED` - Token tidak valid
- `402 INSUFFICIENT_TOKENS` - Saldo token tidak cukup
- `400 VALIDATION_ERROR` - Data assessment tidak valid
- `503 ARCHIVE_SERVICE_ERROR` - Gagal menyimpan job

---

## 📊 Job Monitoring Endpoints

### 1. Get Job Status
**GET** `/api/assessment/status/:jobId`

Mendapatkan status processing assessment berdasarkan job ID.

**Parameters:**
- `jobId` (string) - ID job yang diperoleh dari submit assessment

**Response:**
```json
{
  "success": true,
  "message": "Job status retrieved successfully",
  "data": {
    "jobId": "uuid",
    "status": "queued|processing|completed|failed",
    "progress": 75,
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-01T10:05:00Z",
    "estimatedTimeRemaining": "2 minutes",
    "queuePosition": 1,
    "userId": "uuid",
    "userEmail": "<EMAIL>",
    "resultId": "uuid",
    "assessmentName": "AI-Driven Talent Mapping",
    "error": "string"
  }
}
```

**Status Values:**
- `queued` - Job dalam antrian menunggu processing
- `processing` - Job sedang diproses oleh AI
- `completed` - Job selesai, hasil tersedia di Archive Service
- `failed` - Job gagal, token sudah di-refund

**Error Responses:**
- `401 UNAUTHORIZED` - Token tidak valid
- `403 FORBIDDEN` - Job bukan milik user yang terautentikasi
- `404 NOT_FOUND` - Job tidak ditemukan

### 2. Get Queue Status
**GET** `/api/assessment/queue/status`

Mendapatkan informasi status antrian processing untuk monitoring.

**Response:**
```json
{
  "success": true,
  "message": "Queue status retrieved successfully",
  "data": {
    "queueLength": 15,
    "activeWorkers": 3,
    "averageProcessingTime": "3.2 minutes",
    "estimatedWaitTime": "5-10 minutes",
    "jobStats": {
      "total": 1000,
      "queued": 15,
      "processing": 3,
      "completed": 950,
      "failed": 32
    }
  }
}
```

---

## 🔧 Utility Endpoints

### 1. Idempotency Health Check
**GET** `/api/assessment/idempotency/health`

Mengecek status kesehatan idempotency service.

**Response:**
```json
{
  "success": true,
  "message": "Idempotency service is healthy",
  "data": {
    "status": "healthy",
    "cacheSize": 150,
    "hitRate": 0.85,
    "lastCleanup": "2024-01-01T09:00:00Z"
  }
}
```

### 2. Cleanup Idempotency Cache
**POST** `/api/assessment/idempotency/cleanup`

Membersihkan cache idempotency yang sudah expired (untuk maintenance).

**Response:**
```json
{
  "success": true,
  "message": "Idempotency cache cleaned up successfully",
  "data": {
    "removedEntries": 25,
    "remainingEntries": 125
  }
}
```

---

## 🔍 Health Check

### Service Health
**GET** `/api/assessment/health`

Mengecek status kesehatan service (tidak memerlukan autentikasi).

**Response:**
```json
{
  "status": "healthy|degraded|error",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "assessment-service",
  "version": "1.0.0",
  "dependencies": {
    "rabbitmq": {
      "status": "healthy",
      "details": {
        "messageCount": 5,
        "consumerCount": 2
      }
    },
    "authService": {
      "status": "healthy"
    },
    "archiveService": {
      "status": "healthy"
    }
  },
  "jobs": {
    "total": 1000,
    "queued": 5,
    "processing": 2,
    "completed": 950,
    "failed": 43
  }
}
```

---

## ❌ Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {...}
  }
}
```

### Common Error Codes
- `UNAUTHORIZED` (401) - Token tidak valid atau tidak ada
- `FORBIDDEN` (403) - Akses ditolak ke resource
- `NOT_FOUND` (404) - Resource tidak ditemukan
- `VALIDATION_ERROR` (400) - Data input tidak valid
- `INSUFFICIENT_TOKENS` (402) - Saldo token tidak cukup
- `RATE_LIMIT_EXCEEDED` (429) - Terlalu banyak request
- `ARCHIVE_SERVICE_ERROR` (503) - Error komunikasi dengan Archive Service
- `INTERNAL_ERROR` (500) - Server error

---

## 📝 Usage Flow

### Typical Assessment Flow:
1. **Submit Assessment** - POST `/api/assessment/submit`
2. **Monitor Progress** - GET `/api/assessment/status/:jobId` (polling)
3. **Get Results** - Gunakan Archive Service API dengan `resultId`

### Best Practices:
1. **Idempotency:** Gunakan `X-Idempotency-Key` untuk prevent duplicate submission
2. **Polling:** Check status setiap 10-30 detik, jangan terlalu sering
3. **Error Handling:** Handle semua error codes dengan graceful fallback
4. **Token Management:** Monitor saldo token sebelum submit
5. **Queue Awareness:** Check queue status untuk estimasi waktu tunggu

---

## 🔗 Related Services
- **Auth Service:** Untuk autentikasi dan manajemen token
- **Archive Service:** Untuk mengambil hasil analisis yang sudah selesai
- **API Gateway:** Sebagai entry point untuk semua request eksternal
- **Analysis Worker:** Background service untuk processing assessment (tidak diakses langsung)

## 📊 Performance Notes
- **Processing Time:** 2-5 menit per assessment
- **Queue Capacity:** Dapat handle hingga 1000 concurrent jobs
- **Token Cost:** 1 token per assessment submission
- **Idempotency Window:** 24 jam untuk prevent duplicate
- **Rate Limiting:** 100 submissions per 15 menit per user
