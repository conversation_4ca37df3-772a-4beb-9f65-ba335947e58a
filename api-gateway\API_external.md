# ATMA API Gateway - External API Documentation

## Overview
ATMA API Gateway adalah entry point tunggal untuk semua komunikasi dengan backend services. Gateway ini mengelola routing, authentication, rate limiting, dan security untuk seluruh sistem ATMA (AI-Driven Talent Mapping Assessment).

**Gateway Information:**
- **Service Name:** api-gateway
- **Port:** 3000
- **Base URL:** `http://localhost:3000`
- **Version:** 1.0.0

## 🏗️ Architecture Overview

```
Client Application
       ↓
API Gateway (Port 3000)
       ↓
┌─────────────────┬─────────────────┬─────────────────┐
│  Auth Service   │ Archive Service │Assessment Service│
│   (Port 3001)   │   (Port 3002)   │   (Port 3003)   │
└─────────────────┴─────────────────┴─────────────────┘
```

## 🔐 Authentication

### JWT Token Authentication
Sebagian besar endpoint memerlukan JWT token yang diperoleh dari login.

**Header Required:**
```
Authorization: Bearer <jwt_token>
```

### Internal Service Authentication
Untuk komunikasi antar service (tidak untuk client):
```
X-Service-Key: <internal_service_key>
X-Internal-Service: true
```

## 📊 Rate Limiting

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| General Gateway | 5000 requests | 15 minutes |
| Auth Endpoints | 100 requests | 15 minutes |
| Admin Endpoints | 50 requests | 15 minutes |
| Assessment Endpoints | 100 requests | 15 minutes |
| Archive Endpoints | 5000 requests | 15 minutes |

## 🌐 CORS Configuration

**Allowed Origins:**
- `http://localhost:3000`
- `http://localhost:8080`
- `http://localhost:5173`

**Allowed Methods:** GET, POST, PUT, DELETE, OPTIONS
**Allowed Headers:** Content-Type, Authorization, X-Service-Key, X-Internal-Service

---

## 🔐 Authentication & User Management

### Public Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Batch Register Users
```http
POST /api/auth/register/batch
Content-Type: application/json

{
  "users": [
    {"email": "<EMAIL>", "password": "pass123"},
    {"email": "<EMAIL>", "password": "pass456"}
  ]
}
```

#### User Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "user_type": "user",
      "token_balance": 100
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### Protected User Endpoints

#### Get User Profile
```http
GET /api/auth/profile
Authorization: Bearer <token>
```

#### Update User Profile
```http
PUT /api/auth/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "johndoe",
  "full_name": "John Doe",
  "school_id": 1
}
```

#### Change Password
```http
POST /api/auth/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "oldpass123",
  "newPassword": "newpass456"
}
```

#### Logout
```http
POST /api/auth/logout
Authorization: Bearer <token>
```

#### Get Token Balance
```http
GET /api/auth/token-balance
Authorization: Bearer <token>
```

### School Management

#### Get Schools
```http
GET /api/auth/schools
Authorization: Bearer <token>
```

#### Create School
```http
POST /api/auth/schools
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "SMA Negeri 1 Jakarta",
  "address": "Jl. Sudirman No. 1",
  "city": "Jakarta",
  "province": "DKI Jakarta"
}
```

#### Get Schools by Location
```http
GET /api/auth/schools/by-location?city=Jakarta
Authorization: Bearer <token>
```

#### Get School Users
```http
GET /api/auth/schools/:schoolId/users
Authorization: Bearer <token>
```

---

## 👨‍💼 Admin Management

### Admin Authentication

#### Admin Login
```http
POST /api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### Protected Admin Endpoints

#### Get Admin Profile
```http
GET /api/admin/profile
Authorization: Bearer <admin_token>
```

#### Register New Admin (Superadmin only)
```http
POST /api/admin/register
Authorization: Bearer <superadmin_token>
Content-Type: application/json

{
  "username": "newadmin",
  "email": "<EMAIL>",
  "password": "password123",
  "user_type": "admin"
}
```

---

## 🎯 Assessment Service

### Submit Assessment
```http
POST /api/assessment/submit
Authorization: Bearer <token>
Content-Type: application/json
X-Idempotency-Key: <unique_key> (optional)

{
  "assessmentName": "AI-Driven Talent Mapping",
  "personalityTest": {
    "responses": [1, 2, 3, 4, 5],
    "completedAt": "2024-01-01T10:00:00Z"
  },
  "skillsAssessment": {
    "technicalSkills": {...},
    "softSkills": {...}
  },
  "cognitiveTest": {
    "logicalReasoning": {...},
    "problemSolving": {...}
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "uuid",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 3,
    "tokenCost": 1,
    "remainingTokens": 9
  }
}
```

### Get Assessment Status
```http
GET /api/assessment/status/:jobId
Authorization: Bearer <token>
```

### Get Queue Status
```http
GET /api/assessment/queue/status
Authorization: Bearer <token>
```

### Health Checks
```http
GET /api/assessment/health
GET /api/assessment/health/ready
GET /api/assessment/health/live
GET /api/assessment/health/queue
```

---

## 📊 Archive Service

### Analysis Results

#### Get User Results
```http
GET /api/archive/results?page=1&limit=10&status=completed
Authorization: Bearer <token>
```

#### Get Specific Result
```http
GET /api/archive/results/:id
Authorization: Bearer <token>
```

#### Update Result
```http
PUT /api/archive/results/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "analysis_data": {...},
  "status": "completed"
}
```

#### Delete Result
```http
DELETE /api/archive/results/:id
Authorization: Bearer <token>
```

### Analysis Jobs

#### Get User Jobs
```http
GET /api/archive/jobs?page=1&limit=10&status=completed
Authorization: Bearer <token>
```

#### Get Job Status
```http
GET /api/archive/jobs/:jobId
Authorization: Bearer <token>
```

#### Get Job Statistics
```http
GET /api/archive/jobs/stats
Authorization: Bearer <token>
```

### Statistics

#### Get User Statistics
```http
GET /api/archive/stats
Authorization: Bearer <token>
```

#### Get User Overview
```http
GET /api/archive/stats/overview
Authorization: Bearer <token>
```

#### Unified Statistics API
```http
GET /api/archive/api/v1/stats?type=user&scope=overview&timeRange=30%20days
Authorization: Bearer <token>
```

**Query Parameters:**
- `type`: user, system, demographic, performance
- `scope`: overview, detailed, analysis, summary
- `timeRange`: "1 day", "7 days", "30 days", "90 days"

---

## 🔍 Health & Monitoring

### Gateway Health
```http
GET /
GET /health
GET /health/detailed
GET /health/ready
GET /health/live
```

### Service-Specific Health
```http
GET /api/auth/health
GET /api/archive/health
GET /api/assessment/health
```

---

## ❌ Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {...}
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request / Validation Error
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict (e.g., email exists)
- `429` - Rate Limit Exceeded
- `500` - Internal Server Error
- `503` - Service Unavailable
- `504` - Gateway Timeout

### Common Error Codes
- `VALIDATION_ERROR` - Request validation failed
- `UNAUTHORIZED` - Missing or invalid authentication
- `FORBIDDEN` - Insufficient permissions
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `SERVICE_UNAVAILABLE` - Backend service unavailable
- `GATEWAY_TIMEOUT` - Service request timeout
- `INSUFFICIENT_TOKENS` - Not enough token balance

---

## 🚀 Getting Started

### 1. Register & Login
```javascript
// Register
const registerResponse = await fetch('/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

// Login
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const { data } = await loginResponse.json();
const token = data.token;
```

### 2. Submit Assessment
```javascript
const assessmentResponse = await fetch('/api/assessment/submit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'X-Idempotency-Key': 'unique-key-123'
  },
  body: JSON.stringify({
    assessmentName: 'AI-Driven Talent Mapping',
    personalityTest: { responses: [1,2,3,4,5] },
    skillsAssessment: { /* ... */ },
    cognitiveTest: { /* ... */ }
  })
});

const { data } = await assessmentResponse.json();
const jobId = data.jobId;
```

### 3. Monitor Progress
```javascript
const checkStatus = async (jobId) => {
  const response = await fetch(`/api/assessment/status/${jobId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  const { data } = await response.json();
  return data.status; // 'queued', 'processing', 'completed', 'failed'
};

// Poll every 10 seconds
const pollInterval = setInterval(async () => {
  const status = await checkStatus(jobId);
  
  if (status === 'completed') {
    clearInterval(pollInterval);
    // Get results from archive service
    const results = await fetch(`/api/archive/results/${resultId}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
  }
}, 10000);
```

### 4. Get Results
```javascript
const getResults = async () => {
  const response = await fetch('/api/archive/results', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  const { data } = await response.json();
  return data.results;
};
```

---

## 🔒 Security Best Practices

1. **Always use HTTPS in production**
2. **Store JWT tokens securely** (httpOnly cookies recommended)
3. **Implement token refresh mechanism**
4. **Handle rate limiting gracefully**
5. **Validate all user inputs**
6. **Use idempotency keys for critical operations**
7. **Implement proper error handling**
8. **Monitor token balance before submissions**

---

## 📞 Support & Contact

For technical support or questions about the API:
- **Documentation:** This file and individual service docs
- **Health Checks:** Use `/health` endpoints for service status
- **Error Logs:** Check response error details for debugging

---

**Last Updated:** 2024-01-21
**API Version:** 1.0.0
**Gateway Version:** 1.0.0
